using System;
using System.Collections.Generic;

namespace AIEvent.Application.DTO.Auth
{
    public class AuthResponse
    {
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }

    public class UserInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public List<string> Roles { get; set; } = [];
        public bool EmailConfirmed { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
