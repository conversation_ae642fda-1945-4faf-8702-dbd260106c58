﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace AIEvent.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: new Guid("3c688cd0-9d68-40bb-9780-2d04827e7e0f"));

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: new Guid("82a191c3-87f4-4f22-9fc6-1cabc5255db0"));

            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "CreatedAt", "Description", "Name", "NormalizedName", "UpdatedAt" },
                values: new object[,]
                {
                    { new Guid("5ab01dc8-d562-4504-9544-8bad93542974"), null, new DateTime(2025, 9, 10, 11, 56, 32, 305, DateTimeKind.Utc).AddTicks(4085), "Regular user role", "User", "USER", null },
                    { new Guid("7ca493de-ebfd-48d3-a316-cc1a696320bc"), null, new DateTime(2025, 9, 10, 11, 56, 32, 305, DateTimeKind.Utc).AddTicks(4083), "Administrator role with full access", "Admin", "ADMIN", null }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: new Guid("5ab01dc8-d562-4504-9544-8bad93542974"));

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: new Guid("7ca493de-ebfd-48d3-a316-cc1a696320bc"));

            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "CreatedAt", "Description", "Name", "NormalizedName", "UpdatedAt" },
                values: new object[,]
                {
                    { new Guid("3c688cd0-9d68-40bb-9780-2d04827e7e0f"), null, new DateTime(2025, 9, 10, 10, 36, 59, 800, DateTimeKind.Utc).AddTicks(1004), "Regular user role", "User", "USER", null },
                    { new Guid("82a191c3-87f4-4f22-9fc6-1cabc5255db0"), null, new DateTime(2025, 9, 10, 10, 36, 59, 800, DateTimeKind.Utc).AddTicks(1003), "Administrator role with full access", "Admin", "ADMIN", null }
                });
        }
    }
}
